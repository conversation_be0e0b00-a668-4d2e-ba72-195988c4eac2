version: '3.8'

services:
  frontend:
    image: tongnguyen/frontend:latest
    ports:
      - "80:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://${SERVER_IP}/api/
      - API_URL=http://backend:8080/api/
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  backend:
    image: tongnguyen/backend:latest
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DATABASE_URL=mysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
      - ALLOWED_ORIGINS=http://${SERVER_IP}
    depends_on:
      - db-mysql
    networks:
      - app-network
    restart: unless-stopped

  db-mysql:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - app-network

volumes:
  db_data:

networks:
  app-network:
    driver: bridge
