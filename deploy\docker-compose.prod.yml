services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - frontend
      - backend
    networks:
      - app-network
    restart: unless-stopped

  frontend:
    image: tongnguyen/frontend:latest
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=/api/
      - API_URL=http://backend:8080/api/
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  backend:
    image: tongnguyen/backend:latest
    expose:
      - "8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DB_HOST=db-mysql
      - DB_PORT=3306
      - DB_NAME=DBDKKHAMBENH
      - DB_USER=root
      - DB_PASSWORD=123456
      - DATABASE_URL=mysql://root:123456@db-mysql:3306/DBDKKHAMBENH
      - ALLOWED_ORIGINS=http://**************
    depends_on:
      - db-mysql
    networks:
      - app-network
    restart: unless-stopped

  db-mysql:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_DATABASE=DBDKKHAMBENH
      - MYSQL_ROOT_PASSWORD=123456
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - app-network

volumes:
  db_data:

networks:
  app-network:
    driver: bridge
