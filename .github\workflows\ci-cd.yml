name: 🚀 Simple CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'frontend/**'
      - 'backend/**'
      - '.github/workflows/**'
      - 'package.json'
      - 'docker-compose.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'frontend/**'
      - 'backend/**'
      - '.github/workflows/**'
      - 'package.json'
      - 'docker-compose.yml'

env:
  NODE_VERSION: '18'
  DOCKER_USERNAME: tongnguyen

jobs:
  # ==================== TESTING JOBS ====================

  frontend-tests:
    name: 🧪 Frontend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: 📥 Install dependencies
        run: npm ci --legacy-peer-deps

      - name: 🧪 Run unit tests
        run: npm run test:unit

      - name: 📊 Generate test coverage
        run: npm run test:coverage

      - name: 🏗️ Build application
        run: npm run build

  backend-tests:
    name: 🧪 Backend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: 📥 Install dependencies
        run: npm ci

      - name: 🧪 Run unit tests
        run: npm run test:unit
        env:
          DB_HOST: localhost
          DB_PORT: 3306
          DB_NAME: test_db
          DB_USERNAME: root
          DB_PASSWORD: root

      - name: 📊 Generate test coverage
        run: npm run test:coverage
        env:
          DB_HOST: localhost
          DB_PORT: 3306
          DB_NAME: test_db
          DB_USERNAME: root
          DB_PASSWORD: root

  # ==================== BUILD & PUSH DOCKER IMAGES ====================

  build-and-push-docker:
    name: 🐳 Build & Push Docker Images
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 🛠️ Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}

      - name: 🏷️ Generate tags
        id: meta
        run: |
          echo "timestamp=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT
          echo "short_sha=$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_OUTPUT

      - name: 🧱 Build and push Backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ env.DOCKER_USERNAME }}/backend:latest
            ${{ env.DOCKER_USERNAME }}/backend:${{ steps.meta.outputs.short_sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 🧱 Build and push Frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: |
            ${{ env.DOCKER_USERNAME }}/frontend:latest
            ${{ env.DOCKER_USERNAME }}/frontend:${{ steps.meta.outputs.short_sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # ==================== DEPLOY TO GCP ====================

  deploy-to-gcp:
    name: 🚀 Deploy to GCP
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests, build-and-push-docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: 📦 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.GCP_SSH_PRIVATE_KEY }}

      - name: 🔍 Debug SSH Connection
        run: |
          echo "Testing SSH connection..."
          ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${{ secrets.GCP_USER }}@${{ secrets.GCP_HOST }} "echo 'SSH connection successful!'"
          echo "Checking SSH agent..."
          ssh-add -l

      - name: 📋 Copy deployment files
        run: |
          scp -o StrictHostKeyChecking=no -r deploy/ ${{ secrets.GCP_USER }}@${{ secrets.GCP_HOST }}:~/

      - name: 🚀 Deploy application
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.GCP_USER }}@${{ secrets.GCP_HOST }} '
            cd ~/deploy &&
            echo "📁 Current directory:" && pwd &&
            echo "📋 Files in deploy directory:" && ls -la &&
            echo "🔧 Creating .env file..." &&
            cat > .env << "ENVEOF"
            NODE_ENV=production
            FRONTEND_PORT=80
            BACKEND_PORT=8080
            DB_EXTERNAL_PORT=3306
            NEXT_PUBLIC_API_URL=http://${{ secrets.GCP_HOST }}/api/
            ALLOWED_ORIGINS=http://${{ secrets.GCP_HOST }}
            DB_HOST=db-mysql
            DB_PORT=3306
            DB_NAME=DBDKKHAMBENH
            DB_USER=root
            DB_PASSWORD=${{ secrets.DB_PASSWORD }}
            ENVEOF
            echo "✅ .env file created" &&
            echo "📋 .env file contents:" && cat .env &&
            echo "🔧 Making deploy script executable..." &&
            chmod +x deploy.sh &&
            echo "🚀 Starting deployment..." &&
            ./deploy.sh &&
            echo "🔍 Checking running containers:" &&
            docker ps
          '

      - name: 🔍 Health check
        run: |
          echo "⏳ Waiting for services to start..."
          sleep 60
          echo "🔍 Debug: GCP_HOST = '${{ secrets.GCP_HOST }}'"
          echo "🔍 Testing health check endpoint..."
          if [ -z "${{ secrets.GCP_HOST }}" ]; then
            echo "❌ GCP_HOST secret is empty!"
            exit 1
          fi
          curl -f "http://${{ secrets.GCP_HOST }}:8080/api/health" || exit 1
          echo "✅ Application deployed successfully!"

  # ==================== NOTIFICATIONS ====================

  notifications:
    name: 📢 Notifications
    runs-on: ubuntu-latest
    needs: [frontend-tests, backend-tests, build-and-push-docker, deploy-to-gcp]
    if: always()

    steps:
      - name: 📊 Check job results
        run: |
          echo "Frontend Tests: ${{ needs.frontend-tests.result }}"
          echo "Backend Tests: ${{ needs.backend-tests.result }}"
          echo "Build & Push: ${{ needs.build-and-push-docker.result }}"
          echo "GCP Deploy: ${{ needs.deploy-to-gcp.result }}"

          if [[ "${{ needs.frontend-tests.result }}" == "failure" ||
                "${{ needs.backend-tests.result }}" == "failure" ]]; then
            echo "❌ Some tests failed!"
            exit 1
          elif [[ "${{ needs.deploy-to-gcp.result }}" == "failure" ]]; then
            echo "❌ Deployment failed!"
            exit 1
          else
            echo "✅ All jobs completed successfully!"
          fi
git 