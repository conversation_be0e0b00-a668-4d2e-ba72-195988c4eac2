{"name": "cnpm-websitedkkhambenh", "version": "1.0.0", "description": "Website Đăng Ký Khám Bệnh - CNPM Project", "scripts": {"postinstall": "npm run install:deps", "install:deps": "cd frontend && npm install && cd ../backend && npm install", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "test:all": "powershell -ExecutionPolicy Bypass -File scripts/test-all.ps1", "test:all:bash": "bash scripts/test-all.sh", "test:local": "powershell -ExecutionPolicy Bypass -File scripts/test-local-basic.ps1", "test:backend-only": "powershell -ExecutionPolicy Bypass -File scripts/test-backend-only.ps1", "test:frontend-only": "powershell -ExecutionPolicy Bypass -File scripts/test-frontend-only.ps1", "pre-commit": "powershell -ExecutionPolicy Bypass -File scripts/pre-commit-test.ps1", "cleanup:history": "powershell -ExecutionPolicy Bypass -File scripts/cleanup-git-history-safe.ps1", "lint": "npm run lint:frontend", "lint:frontend": "cd frontend && npm run lint", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && echo 'Backend build completed'", "start": "cd backend && npm start", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "docker:build": "docker build -t frontend ./frontend && docker build -t backend ./backend", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"multer": "^2.0.1"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["healthcare", "appointment", "booking", "nodejs", "react", "mysql"], "author": "CNPM Team", "license": "MIT"}