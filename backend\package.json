{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "jest --testPathPattern=__tests__/unit", "test:e2e": "jest --testPathPattern=__tests__/e2e", "test:legacy": "node test.js", "start": "node src/server.js", "dev": "nodemon src/server.js", "lint": "echo \"Linting backend code...\"", "health-check": "curl -f http://localhost:8080/health || exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cli": "^1.0.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "faker": "^6.6.6", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.12.0", "path": "^0.12.7", "sequelize": "^6.37.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tedious": "^18.6.1"}, "devDependencies": {"@faker-js/faker": "^9.5.0", "nodemon": "^3.1.9", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "testPathIgnorePatterns": ["<rootDir>/test.js", "<rootDir>/__tests__/integration/"], "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/**"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}}